import "@/styles/globals.css"
import "@sendbird/uikit-react/dist/index.css"

import { type Metadata } from "next"
import { Open_Sans } from "next/font/google"
import { type Session } from "next-auth"

import AnalyticsBody from "@/components/analytics/body"
import AnalyticsHead from "@/components/analytics/head"

import ClientProviders from "./ClientProviders"
import getServerSessionLayout from "@/authentication/getServerSessionLayout"
import { PERSISTED_GQL } from "@/commons/flags"
import { AUTH } from "@/config/client"
import { initFeatureFlags } from "@/utils/featureFlag/initFeatureFlags"

const openSans = Open_Sans({
  subsets: ["latin"],
  variable: "--font-openSans",
  display: "swap",
})

type TRootLayoutProps = {
  children: React.ReactNode
  session: Session
}

export default async function Page(props: TRootLayoutProps) {
  const session = await getServerSessionLayout()

  // Init feature flags to cookies
  await initFeatureFlags([PERSISTED_GQL], {
    userId: session?.user?.id,
    institutionId: session?.user?.institutionId,
  })

  return (
    <html className={openSans.variable} id="admin-app">
      <head>
        <AnalyticsHead />
      </head>
      <body>
        <AnalyticsBody />
        <ClientProviders session={session}>{props.children}</ClientProviders>
      </body>
    </html>
  )
}

export const metadata: Metadata = {
  metadataBase: new URL(AUTH.BASE_URL),
  title: {
    default: "INAPROC Pusat Kendali",
    template: "%s | INAPROC Pusat Kendali",
  },
  description: "INAPROC Manajemen Penyedia",
  icons: [{ rel: "icon", url: "/assets/icons/favicon.ico" }],
  openGraph: {
    title: "INAPROC Pusat Kendali",
    siteName: "INAPROC Pusat Kendali",
    description: "INAPROC Manajemen Penyedia",
    url: AUTH.BASE_URL,
    locale: "id_ID",
    type: "website",
  },
}

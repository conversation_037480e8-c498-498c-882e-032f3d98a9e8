import {
  type RequestMiddleware,
  type ResponseMiddleware,
} from "graphql-request"
import {
  checkUnauthorized,
  graphqlClientMiddlewareV2,
  IS_DEVELOPMENT,
  IS_STAGING,
  PersistedQueryGraphQLClient,
} from "shared-utils"

import { signIn, signOut } from "@/authentication/authService"
import { PERSISTED_GQL } from "@/commons/flags"
import { GRAPHQL_URL } from "@/config/api"
import { AUTH } from "@/config/client"
import persistedDocuments from "@/federatedGql/persisted-documents.json"
import { isEnabled } from "@/utils/featureFlag/initFeatureFlags"

const requestMiddleware: RequestMiddleware = (request) => {
  return graphqlClientMiddlewareV2({
    request,
    signOut,
    app: "INTERNAL",
    baseUrl: AUTH.BASE_URL,
  })
}

export const responseMiddleware: ResponseMiddleware = (response) => {
  if (checkUnauthorized(response)) {
    window && signIn("refreshToken")
  }
}

export const federatedGqlClient = new PersistedQueryGraphQLClient(
  GRAPHQL_URL,
  {
    requestMiddleware,
    responseMiddleware,
    credentials: "include",
  },
  {
    persistedDocuments: persistedDocuments as Record<string, string>,
    includeFallbackQuery: IS_DEVELOPMENT || IS_STAGING,
    debug: IS_DEVELOPMENT || IS_STAGING,
    enabled: isEnabled(PERSISTED_GQL),
  }
)

/**
 * Utility functions for reading cookies on both client and server side
 */

/**
 * Parse a cookie string and return a key-value object
 */
export function parseCookies(cookieString: string): Record<string, string> {
  const cookies: Record<string, string> = {}
  
  if (!cookieString) return cookies
  
  cookieString.split(";").forEach((cookie) => {
    const [name, ...rest] = cookie.trim().split("=")
    if (name && rest.length > 0) {
      cookies[name] = rest.join("=")
    }
  })
  
  return cookies
}

/**
 * Get a specific cookie value from document.cookie (client-side only)
 */
export function getCookieValue(name: string): string | undefined {
  if (typeof window === "undefined") {
    throw new Error("getCookieValue can only be used on the client-side")
  }
  
  const cookies = parseCookies(document.cookie)
  return cookies[name]
}

/**
 * Check if a cookie exists and has a specific value
 */
export function isCookieTrue(name: string): boolean {
  if (typeof window === "undefined") {
    return false
  }
  
  return getCookieValue(name) === "true"
}

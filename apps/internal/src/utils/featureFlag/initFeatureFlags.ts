import { getC<PERSON><PERSON>, set<PERSON><PERSON><PERSON>, has<PERSON><PERSON><PERSON> } from "cookies-next"

import getFlag from "./getFlag"

/**
 * Hydrates server-side feature flags to cookies
 * This function should be called in server components or layouts
 */
export async function initFeatureFlags(
  flagNames: string[],
  props?: {
    userId?: string
    sessionId?: string
    institutionId?: string
  }
): Promise<void> {
  // This function should only be called on the server-side
  if (typeof window !== "undefined") {
    throw new Error("hydrateFeatureFlags can only be called on the server-side")
  }

  try {
    const { isEnabled: isEnabledFlag } = await getFlag(props)

    // Hydrate each flag to cookies with FLAG_ prefix
    for (const flagName of flagNames) {
      const cookieName = `FLAG_${flagName}`
      const flagValue = isEnabledFlag(flagName)

      // Set the cookie using cookies-next (expires in 1 hour, can be adjusted)
      setCookie(cookieName, flagValue ? "true" : "false", {
        req: undefined, // Will use Next.js cookies() internally
        res: undefined,
        httpOnly: false, // Allow client-side access
        secure: process.env.NODE_ENV === "production",
        sameSite: "lax",
        maxAge: 60 * 60, // 1 hour
        path: "/",
      })
    }
  } catch (error) {
    console.error("Failed to hydrate feature flags:", error)

    // Set default values on error
    for (const flagName of flagNames) {
      const cookieName = `FLAG_${flagName}`
      setCookie(cookieName, "false", {
        req: undefined,
        res: undefined,
        httpOnly: false,
        secure: process.env.NODE_ENV === "production",
        sameSite: "lax",
        maxAge: 60 * 60,
        path: "/",
      })
    }
  }
}

/**
 * Get a hydrated feature flag value from cookies
 * This can be used on both client and server side
 */
export function isEnabled(featureFlag: string): boolean {
  const cookieName = `FLAG_${featureFlag}`
  if (!hasCookie(cookieName)) return false
  const cookieValue = getCookie(cookieName)
  return cookieValue === "true"
}

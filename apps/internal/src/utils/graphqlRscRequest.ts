import "server-only"

import { type TypedDocumentNode } from "@graphql-typed-document-node/core"
import { type RequestMiddleware, type Variables } from "graphql-request"
import type { VariablesAndRequestHeadersArgs } from "graphql-request/build/legacy/helpers/types.js"
import { type NextApiRequest } from "next"
import { getServerSession } from "next-auth"
import {
  checkUnauthorized,
  PersistedQueryGraphQLClient,
  IS_DEVELOPMENT,
  IS_STAGING,
} from "shared-utils"
import { graphqlRscMiddleware } from "shared-utils/accessToken"

import { responseMiddleware } from "./graphqlClient"
import { authOptions } from "@/authentication/authOptions"
import { PERSISTED_GQL } from "@/commons/flags"
import { GRAPHQL_URL } from "@/config/api"
import persistedDocuments from "@/federatedGql/persisted-documents.json"
import { isEnabled } from "@/utils/featureFlag/initFeatureFlags"

const requestMiddleware: RequestMiddleware = async (request) => {
  const resultMiddleware = await graphqlRscMiddleware({
    request,
    getServerSession: () =>
      getServerSession(
        authOptions({ req: request as NextApiRequest, forceRefresh: true })
      ),
  })

  return resultMiddleware
}

const graphqlRequest = new PersistedQueryGraphQLClient(
  GRAPHQL_URL,
  {
    requestMiddleware,
    responseMiddleware,
    credentials: "include",
  },
  {
    persistedDocuments: persistedDocuments as Record<string, string>,
    includeFallbackQuery: IS_DEVELOPMENT || IS_STAGING,
    enabled: isEnabled(PERSISTED_GQL),
    debug: IS_DEVELOPMENT || IS_STAGING,
  }
)

/**
 * graphqlRscRequest should only be used from a Server Component.
 */
export const graphqlRscRequest = async <T, V extends Variables = Variables>(
  document: TypedDocumentNode<T, V>,
  ...variablesAndRequestHeaders: VariablesAndRequestHeadersArgs<V>
): Promise<T> => {
  try {
    return graphqlRequest.request(document, ...variablesAndRequestHeaders)
  } catch (e) {
    if (checkUnauthorized(e)) {
      return graphqlRequest.request(document, ...variablesAndRequestHeaders)
    } else {
      throw e
    }
  }
}

import {
  IS_PRODUCTION,
  IS_DEVELOPMENT,
  IS_STAGING,
} from "shared-utils/constants"

export const ENTERPRISE_VALIDATION = "MARKETPLACE-ENTERPRISE_VALIDATION"

export const PRODUCT_PRINCIPAL_SECTORAL =
  "MARKETPLACE-PRODUCT_PRINCIPAL_SECTORAL"

export const METABASE_AKUN_INAPROC = "ACCOUNT-METABASE-AKUN-INAPROC"

export const SHOW_SOCKET_GET_UPLOAD_STATUS_V1 =
  "ACCOUNT-KILL_SWICTH_AUTH_UPLOAD_STATUS_SOCKET"

export const ORDER_CONFIRMATION_DELIVERY_DOCUMENT =
  "ORDER-SERVICEDIGITAL_CONFIRMATION_DELIVERY_DOCUMENT"

export const MARKETPLACE_MINICOM_ADJUSTMENT = "MARKETPLACE-MINICOM_ADJUSTMENT"

export const AUDIT_E_KATALOG_V6 = !IS_PRODUCTION

export const MARKETPLACE_MASTER_PRODUCT = "MARKETPLACE-MASTER_PRODUCT"

export const MARKETPLACE_INTERNAL_PRODUCT_TAX_SIMULATION =
  "MARKETPLACE-INTERNAL_PRODUCT_TAX_SIMULATION"

export const PAYMENT_RESET_PAYMENT = "PAYMENT-RESET_PAYMENT"

export const MARKETPLACE_PPNBM_DECLARATION = "MARKETPLACE-PPNBM_DECLARATION"

export const MARKETPLACE_ADDONS_TAX_FREE = "MARKETPLACE-ADDONS_TAX_FREE"

export const MARKETPLACE_MULTISHIPMENT = "MARKETPLACE-MULTISHIPMENT_INTERNAL"

export const MARKETPLACE_ADJUSTMENT_DOWN_PAYMENT =
  "MARKETPLACE-ADJUSTMENT_DOWN_PAYMENT"

export const MARKETPLACE_ADJUSTMENT_RETENSI = "MARKETPLACE-ADJUSTMENT_RETENSI"

export const IS_DEVELOPMENT_OR_STAGING = IS_STAGING || IS_DEVELOPMENT

export const MARKETPLACE_FTZ_MULTISHIPMENT = "MARKETPLACE-FTZ_MULTISHIPMENT"

export const MARKETPLACE_FTZ_SINGLESHIPMENT = "MARKETPLACE-FTZ_SINGLESHIPMENT"
export const ORDER_ADD_CONFIRMATION_DATE = "ORDER_ADD-CONFIRMATION-DATE"

export const MARKETPLACE_MULTISHIPMENT_INTERNAL =
  "MARKETPLACE-MULTISHIPMENT_INTERNAL"
export const MARKETPLACE_MULTIPLE_PIC_TTD = "MARKETPLACE-MULTIPLE_PIC_TTD"

export const ORDER_TAX_CORETAX_ADJUSTMENT = "TAX_CORETAX_ADJUSTMENT"

export const PERSISTED_GQL = "PERSISTED_GQL"

import { type TypedDocumentNode } from "@graphql-typed-document-node/core"
import { print } from "graphql"
import {
  GraphQLClient,
  type GraphQLResponse,
  type RequestDocument,
  type Variables,
  type RequestOptions,
} from "graphql-request"
import {
  type VariablesAndRequestHeadersArgs,
  type RequestConfig,
} from "graphql-request/build/legacy/helpers/types"

/**
 * Configuration for persisted query client wrapper
 */
export interface PersistedQueryGraphQLClientConfig {
  /**
   * The persisted documents manifest (query hash -> query string mapping)
   */
  persistedDocuments: Record<string, string>
  /**
   * Whether to enable persisted queries. When false, behaves like regular GraphQLClient
   * @default true
   */
  enabled?: boolean
  /**
   * Whether to include the full query as fallback (for development)
   * @default false
   */
  includeFallbackQuery?: boolean
  /**
   * Enable debug logging for persisted queries
   * @default false
   */
  debug?: boolean
}

/**
 * Check if the response contains a "PersistedQueryNotFound" error
 */
function isPersistedQueryNotFoundError(response: unknown): boolean {
  try {
    const responseObj = response as GraphQLResponse
    if (responseObj?.errors) {
      return responseObj.errors.some(
        (error) =>
          error.extensions?.code === "PERSISTED_QUERY_NOT_FOUND" ||
          error.message?.includes("PersistedQueryNotFound") ||
          error.message?.includes("persisted query not found")
      )
    }
    return false
  } catch {
    return false
  }
}

/**
 * Check if an error is a persisted query not found error from various sources
 */
function isPersistedQueryError(error: unknown): boolean {
  // Check if it's a GraphQL response error
  if (isPersistedQueryNotFoundError(error)) {
    return true
  }

  // Check if it's an error object with message
  if (error && typeof error === "object" && "message" in error) {
    const message = (error as { message: string }).message
    return (
      message?.includes("PersistedQueryNotFound") ||
      message?.includes("persisted query not found")
    )
  }

  return false
}

/**
 * Normalize a GraphQL query string for comparison
 */
function normalizeQuery(query: string): string {
  return query.replace(/\s+/g, " ").trim()
}

/**
 * Find the persisted document ID for a given query
 */
function findPersistedDocumentId(
  query: string,
  persistedDocuments: Record<string, string>
): string | undefined {
  const normalizedQuery = normalizeQuery(query)

  // Look for the query in the persisted documents manifest
  for (const [id, persistedQuery] of Object.entries(persistedDocuments)) {
    const normalizedPersistedQuery = normalizeQuery(persistedQuery)
    if (normalizedQuery === normalizedPersistedQuery) {
      return id
    }
  }

  return undefined
}

/**
 * Create a persisted query document with hash
 */
function createPersistedQueryDocument<T, V>(persistedDocumentId: string) {
  return {
    extensions: {
      persistedQuery: {
        version: 1,
        sha256Hash: persistedDocumentId,
      },
    },
  } as unknown as TypedDocumentNode<T, V>
}

/**
 * GraphQL client that extends the base GraphQLClient with persisted query support
 */
export default class PersistedQueryGraphQLClient extends GraphQLClient {
  private config: PersistedQueryGraphQLClientConfig

  constructor(
    url: string,
    requestConfig?: RequestConfig,
    config?: PersistedQueryGraphQLClientConfig
  ) {
    super(url, requestConfig)
    this.config = {
      persistedDocuments: {},
      enabled: true,
      includeFallbackQuery: false,
      debug: false,
      ...config,
    }

    // Validate configuration
    if (
      this.config.persistedDocuments &&
      typeof this.config.persistedDocuments !== "object"
    ) {
      throw new Error("persistedDocuments must be an object")
    }

    this.debugLog("PersistedQueryGraphQLClient initialized", {
      persistedDocumentsCount: Object.keys(this.config.persistedDocuments)
        .length,
      enabled: this.config.enabled,
      includeFallbackQuery: this.config.includeFallbackQuery,
      debug: this.config.debug,
    })
  }

  /**
   * Extract query string from document with error handling
   */
  private extractQueryString<T, V>(
    document: RequestDocument | TypedDocumentNode<T, V>
  ): string {
    try {
      if (typeof document === "string") {
        return document
      }

      if (!document) {
        throw new Error("Document is null or undefined")
      }

      return print(document)
    } catch (error) {
      this.debugLog("Error extracting query string", { error, document })
      throw new Error(
        `Failed to extract query string: ${
          error instanceof Error ? error.message : "Unknown error"
        }`
      )
    }
  }

  /**
   * Log debug information if debug mode is enabled
   */
  private debugLog(message: string, data?: unknown): void {
    if (this.config.debug) {
      console.log(`[PersistedQueryGraphQLClient] ${message}`, data)
    }
  }

  /**
   * Execute persisted query request with retry logic using options signature
   */
  private async executePersistedQueryWithOptions<
    T,
    V extends Variables = Variables
  >(options: RequestOptions<V, T>, queryString: string): Promise<T> {
    // If persisted queries are disabled, use regular GraphQL client behavior
    if (!this.config.enabled) {
      this.debugLog("Persisted queries disabled, using regular GraphQL client")
      return super.request<T, V>(options)
    }

    // If includeFallbackQuery is enabled, send full query immediately
    if (this.config.includeFallbackQuery) {
      this.debugLog("Using fallback query (includeFallbackQuery enabled)")
      return super.request<T, V>(options)
    }

    // Try to find the persisted document ID
    const persistedDocumentId = findPersistedDocumentId(
      queryString,
      this.config.persistedDocuments
    )

    // If no persisted document found, fall back to normal query
    if (!persistedDocumentId) {
      this.debugLog("No persisted document found, falling back to full query")
      return super.request<T, V>(options)
    }

    this.debugLog("Found persisted document ID", { persistedDocumentId })

    try {
      // First attempt: send only the persisted query hash
      const persistedQueryDocument = createPersistedQueryDocument<T, V>(
        persistedDocumentId
      )

      const persistedOptions: RequestOptions<V, T> = {
        ...options,
        document: persistedQueryDocument,
      }

      this.debugLog("Attempting persisted query request")
      return await super.request<T, V>(persistedOptions)
    } catch (error) {
      // Check if it's a persisted query not found error
      if (isPersistedQueryError(error)) {
        this.debugLog(
          "Persisted query not found on server, retrying with full query",
          { error }
        )
        // Retry with the full query
        return super.request<T, V>(options)
      }

      this.debugLog("Non-persisted query error occurred", { error })
      // Re-throw other errors
      throw error
    }
  }

  /**
   * Request method with persisted query support
   * Supports both document with variables/headers signature and options signature
   */
  async request<T, V extends Variables = Variables>(
    document: RequestDocument | TypedDocumentNode<T, V>,
    ...variablesAndRequestHeaders: VariablesAndRequestHeadersArgs<V>
  ): Promise<T>
  async request<T, V extends Variables = Variables>(
    options: RequestOptions<V, T>
  ): Promise<T>
  async request<T, V extends Variables = Variables>(
    documentOrOptions:
      | RequestDocument
      | TypedDocumentNode<T, V>
      | RequestOptions<V, T>,
    ...variablesAndRequestHeaders: VariablesAndRequestHeadersArgs<V>
  ): Promise<T> {
    // Check if first argument is options object
    if (
      typeof documentOrOptions === "object" &&
      documentOrOptions !== null &&
      "document" in documentOrOptions
    ) {
      // Handle options signature
      const options = documentOrOptions as RequestOptions<V, T>
      const queryString = this.extractQueryString(options.document)
      return this.executePersistedQueryWithOptions<T, V>(options, queryString)
    } else {
      // Handle document with variables/headers signature
      const document = documentOrOptions as
        | RequestDocument
        | TypedDocumentNode<T, V>
      const queryString = this.extractQueryString(document)
      const options = {
        document,
        variables: variablesAndRequestHeaders?.[0],
        requestHeaders: variablesAndRequestHeaders?.[1],
      } as RequestOptions<V, T>

      return this.executePersistedQueryWithOptions<T, V>(options, queryString)
    }
  }
}
